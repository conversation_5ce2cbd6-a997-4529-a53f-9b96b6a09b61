/**
 * ✅ UNIVERSAL EBAY HANDLER - Handles basic messages on all eBay pages
 * Loads at document_start for immediate availability
 * Based on EbayLister4 reference pattern
 */

(function() {
  'use strict';
  
  console.log('🌐 Universal eBay Handler loading at document_start...');
  
  /**
   * Handle basic messages that don't require complex dependencies
   */
  function handleMessage(message, sender, sendResponse) {
    console.log('📨 Universal eBay Handler received message:', message.action);
    
    try {
      // Handle ping requests
      if (message.action === 'ping') {
        console.log('🏓 Universal handler responding to ping');
        sendResponse({
          success: true,
          message: 'pong',
          handler: 'universal-ebay-handler',
          url: window.location.href,
          timestamp: Date.now()
        });
        return true;
      }
      
      // Handle content script readiness check
      if (message.action === 'checkContentScript') {
        console.log('✅ Universal handler confirming ready');
        sendResponse({
          success: true,
          ready: true,
          handler: 'universal-ebay-handler',
          url: window.location.href
        });
        return true;
      }
      
      // Handle startScraping directly as universal fallback
      if (message.action === 'startScraping') {
        console.log('🔄 Universal handler: Handling startScraping directly as fallback');
        console.log('🔍 Universal handler: Message details:', message);
        console.log('🔍 Universal handler: Current URL:', window.location.href);
        console.log('🔍 Universal handler: Page title:', document.title);
        console.log('🔍 Universal handler: Document ready state:', document.readyState);

        // Use async pattern to keep message port open
        (async () => {
          try {
            // ✅ REFERENCE-BASED SELECTORS - Based on working EbayLister4 patterns
            console.log('🔍 Universal handler: Using reference-based eBay selectors...');

            // Primary selector - this is what the reference tool uses
            let listings = document.querySelectorAll('.s-item');
            console.log(`🔍 Universal handler: Found ${listings.length} .s-item elements`);

            // If no .s-item elements, try alternative containers
            if (listings.length === 0) {
              console.log('🔍 Universal handler: No .s-item found, trying alternative selectors...');
              const alternativeSelectors = [
                '.srp-results .s-item',
                '[data-testid="item-card"]',
                '.s-item-container',
                '.su-card-container'
              ];

              for (const selector of alternativeSelectors) {
                listings = document.querySelectorAll(selector);
                console.log(`🔍 Universal handler: Trying "${selector}" - found ${listings.length} elements`);
                if (listings.length > 0) break;
              }
            }

            // ✅ NO FILTERING - Process ALL elements to see everything
            console.log(`🔍 Universal handler: NO FILTERING - Processing all ${listings.length} elements`);

            console.log(`🔍 Universal handler: Processing ALL ${listings.length} listings with NO FILTERING`);

            // ✅ NO FILTERING - EXTRACT EVERYTHING
            const products = [];
            for (let i = 0; i < listings.length; i++) {
              const listing = listings[i];

              // ✅ EXTRACT EVERYTHING - Try multiple selector patterns
              let titleEl = listing.querySelector('.s-item__title') ||
                           listing.querySelector('.s-item__title-tag') ||
                           listing.querySelector('[class*="title"]') ||
                           listing.querySelector('h3') ||
                           listing.querySelector('a[href*="/itm/"]');

              let priceEl = listing.querySelector('.s-item__price') ||
                           listing.querySelector('.s-card__price') ||
                           listing.querySelector('[class*="price"]') ||
                           listing.querySelector('[class*="Price"]');

              let linkEl = listing.querySelector('.s-item__link') ||
                          listing.querySelector('a[href*="/itm/"]') ||
                          listing.querySelector('a[href*="ebay.com"]');

              // ✅ EXTRACT EVERYTHING - NO VALIDATION
              const title = titleEl?.textContent?.trim() || `No Title Found ${i}`;
              const priceText = priceEl?.textContent?.trim() || `No Price Found ${i}`;
              const link = linkEl?.href || '';

              // Extract price number
              const priceMatch = priceText.match(/[\d,]+\.?\d*/);
              const price = priceMatch ? parseFloat(priceMatch[0].replace(/,/g, '')) : 0;

              // Extract item ID from link
              let itemId = '';
              if (link) {
                const itemIdMatch = link.match(/\/itm\/(\d+)/);
                if (itemIdMatch) {
                  itemId = itemIdMatch[1];
                }
              }

              console.log(`📦 Extracted item ${i + 1}: "${title}" - ${priceText}`);

              products.push({
                title: title,
                price: price,
                priceText: priceText,
                link: link,
                itemId: itemId,
                source: 'universal-handler-no-filtering',
                index: i,
                timestamp: Date.now()
              });
            }

            // ✅ ENHANCED EMERGENCY EXTRACTION - If still no products found
            if (products.length === 0) {
              console.log('🚨 Universal handler: Emergency extraction - using broader search patterns');

              // Try to find any elements that might contain product information
              const potentialContainers = document.querySelectorAll('div, li, article, section');
              console.log(`🔍 Universal handler: Scanning ${potentialContainers.length} potential containers`);

              for (let i = 0; i < Math.min(potentialContainers.length, 100); i++) {
                const container = potentialContainers[i];
                const containerText = container.textContent || '';

                // Look for price patterns in the container
                const priceMatch = containerText.match(/\$[\d,]+\.?\d*/);
                if (priceMatch && containerText.length < 500) { // Not too much text (likely not a full page)

                  // Look for title-like text
                  const titleCandidates = container.querySelectorAll('h1, h2, h3, h4, a, span, div');
                  let bestTitle = '';
                  let bestLink = '';

                  for (const candidate of titleCandidates) {
                    const text = candidate.textContent?.trim() || '';
                    const href = candidate.href || '';

                    // Look for eBay item links
                    if (href && href.includes('/itm/')) {
                      bestLink = href;
                      if (text.length > bestTitle.length && text.length > 10 && text.length < 200) {
                        bestTitle = text;
                      }
                    }
                    // Or just good title text
                    else if (text.length > 10 && text.length < 200 &&
                             !text.includes('Category') &&
                             !text.includes('Filter') &&
                             !text.includes('Sort') &&
                             text.length > bestTitle.length) {
                      bestTitle = text;
                    }
                  }

                  if (bestTitle.length > 5) {
                    const price = parseFloat(priceMatch[0].replace(/[$,]/g, ''));
                    const itemId = bestLink ? (bestLink.match(/\/itm\/(\d+)/) || [])[1] || '' : '';

                    console.log(`🚨 Emergency extracted: "${bestTitle}" - ${priceMatch[0]}`);
                    products.push({
                      title: bestTitle,
                      price: price,
                      priceText: priceMatch[0],
                      link: bestLink,
                      itemId: itemId,
                      source: 'emergency-extraction-enhanced',
                      index: products.length,
                      timestamp: Date.now()
                    });

                    // Limit emergency extraction to avoid too many false positives
                    if (products.length >= 10) break;
                  }
                }
              }
            }

            console.log(`✅ Universal handler: Extracted ${products.length} products`);
            console.log('🔍 Universal handler: Sample products:', products.slice(0, 3));
            console.log('🔍 Universal handler: Sending response with products array');

            sendResponse({
              success: true,
              products: products,
              handler: 'universal-ebay-handler',
              action: message.action,
              mode: 'universal-scraping',
              totalFound: products.length,
              url: window.location.href,
              timestamp: Date.now()
            });
          } catch (error) {
            console.error('❌ Universal handler scraping failed:', error);
            sendResponse({
              success: false,
              error: 'Universal scraping failed: ' + error.message,
              handler: 'universal-ebay-handler'
            });
          }
        })();

        return true; // Keep message channel open
      }

      // ✅ FIXED: Always handle startScraping in universal handler
      // The search handler delegation was causing the scraping to fail
      console.log('🔄 Universal handler: All messages handled by universal handler for reliability');
      


      // Handle other basic messages
      console.log('❓ Universal handler - unhandled message:', message.action);
      sendResponse({
        success: false,
        error: 'Message not handled by universal handler',
        action: message.action,
        handler: 'universal-ebay-handler',
        suggestion: 'This message might need the search page handler'
      });
      
    } catch (error) {
      console.error('❌ Universal handler error:', error);
      sendResponse({
        success: false,
        error: 'Universal handler crashed: ' + error.message,
        action: message.action
      });
    }
    
    return true;
  }
  
  /**
   * Check if current page is a search page
   */
  function isSearchPage() {
    const url = window.location.href;
    return url.includes('/sch/i.html') && url.includes('_nkw=');
  }
  
  /**
   * Check if message is search-specific
   */
  function isSearchSpecificMessage(action) {
    const searchActions = [
      'startScraping',
      'testSelectors', 
      'minimalTest',
      'scanCurrentPage'
    ];
    return searchActions.includes(action);
  }
  
  // ✅ CRITICAL: Register message listener immediately
  chrome.runtime.onMessage.addListener(handleMessage);
  
  // Send ready notification to service worker
  function notifyServiceWorker() {
    chrome.runtime.sendMessage({
      action: 'contentScriptReady',
      handler: 'universal-ebay-handler',
      url: window.location.href,
      timestamp: Date.now(),
      isSearchPage: isSearchPage()
    }).catch(error => {
      console.log('📡 Service worker not ready yet for universal handler');
    });
  }
  
  // Notify service worker when ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', notifyServiceWorker);
  } else {
    notifyServiceWorker();
  }
  
  // Global status for debugging
  window.universalEbayHandler = {
    isReady: () => true,
    getUrl: () => window.location.href,
    isSearchPage: isSearchPage
  };
  
  console.log('✅ Universal eBay Handler setup complete');
  
})();
