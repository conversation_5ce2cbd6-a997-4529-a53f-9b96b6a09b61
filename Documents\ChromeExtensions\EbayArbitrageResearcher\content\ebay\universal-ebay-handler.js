/**
 * ✅ UNIVERSAL EBAY HANDLER - Handles basic messages on all eBay pages
 * Loads at document_start for immediate availability
 * Based on EbayLister4 reference pattern
 */

(function() {
  'use strict';
  
  console.log('🌐 Universal eBay Handler loading at document_start...');
  
  /**
   * Handle basic messages that don't require complex dependencies
   */
  function handleMessage(message, sender, sendResponse) {
    console.log('📨 Universal eBay Handler received message:', message.action);
    
    try {
      // Handle ping requests
      if (message.action === 'ping') {
        console.log('🏓 Universal handler responding to ping');
        sendResponse({
          success: true,
          message: 'pong',
          handler: 'universal-ebay-handler',
          url: window.location.href,
          timestamp: Date.now()
        });
        return true;
      }
      
      // Handle content script readiness check
      if (message.action === 'checkContentScript') {
        console.log('✅ Universal handler confirming ready');
        sendResponse({
          success: true,
          ready: true,
          handler: 'universal-ebay-handler',
          url: window.location.href
        });
        return true;
      }
      
      // Handle startScraping directly as universal fallback
      if (message.action === 'startScraping') {
        console.log('🔄 Universal handler: Handling startScraping directly as fallback');
        console.log('🔍 Universal handler: Message details:', message);
        console.log('🔍 Universal handler: Current URL:', window.location.href);
        console.log('🔍 Universal handler: Page title:', document.title);
        console.log('🔍 Universal handler: Document ready state:', document.readyState);

        // Use async pattern to keep message port open
        (async () => {
          try {
            // ✅ AGGRESSIVE ELEMENT FINDING - Try everything possible
            console.log('🔍 Universal handler: AGGRESSIVE element finding...');

            // Try ALL selectors and pick the one with MOST elements (like before)
            const allSelectors = [
              '.s-item',
              '.srp-results .s-item',
              '[data-testid="item-card"]',
              '.s-item-container',
              '.su-card-container',
              'li[class*="s-item"]',
              'div[class*="s-item"]',
              '[class*="item"]',
              'li',
              'div'
            ];

            let listings = [];
            let bestSelector = '';
            for (const selector of allSelectors) {
              const found = document.querySelectorAll(selector);
              console.log(`🔍 Universal handler: "${selector}" found ${found.length} elements`);
              if (found.length > listings.length) {
                listings = found;
                bestSelector = selector;
                console.log(`🔍 Universal handler: NEW BEST: "${selector}" with ${found.length} elements`);
              }
            }

            console.log(`🔍 Universal handler: FINAL CHOICE: "${bestSelector}" with ${listings.length} elements`);

            console.log(`🔍 Universal handler: FINAL SELECTION: ${listings.length} elements`);

            // ✅ NO FILTERING - Process ALL elements to see everything
            console.log(`🔍 Universal handler: NO FILTERING - Processing all ${listings.length} elements`);

            // ✅ DEBUG: Log first few elements to see what we're working with
            for (let i = 0; i < Math.min(5, listings.length); i++) {
              const el = listings[i];
              console.log(`🔍 Element ${i}: tag="${el.tagName}" class="${el.className}" text="${el.textContent?.substring(0, 100)}"`);
            }

            console.log(`🔍 Universal handler: Processing ALL ${listings.length} listings with NO FILTERING`);

            // ✅ SMART LIMITING - Process reasonable number for testing
            const maxItemsToProcess = 100; // Reasonable limit for testing
            const itemsToProcess = Math.min(listings.length, maxItemsToProcess);
            console.log(`🔍 Universal handler: Processing ${itemsToProcess} of ${listings.length} elements for testing`);

            const products = [];
            for (let i = 0; i < itemsToProcess; i++) {
              const listing = listings[i];

              // ✅ EXTRACT EVERYTHING - Try multiple selector patterns
              let titleEl = listing.querySelector('.s-item__title') ||
                           listing.querySelector('.s-item__title-tag') ||
                           listing.querySelector('[class*="title"]') ||
                           listing.querySelector('h3') ||
                           listing.querySelector('a[href*="/itm/"]');

              let priceEl = listing.querySelector('.s-item__price') ||
                           listing.querySelector('.s-card__price') ||
                           listing.querySelector('[class*="price"]') ||
                           listing.querySelector('[class*="Price"]');

              let linkEl = listing.querySelector('.s-item__link') ||
                          listing.querySelector('a[href*="/itm/"]') ||
                          listing.querySelector('a[href*="ebay.com"]');

              // ✅ ZERO FILTERING - EXTRACT EVERYTHING RAW
              const title = titleEl?.textContent?.trim() || `No Title Found ${i}`;
              const priceText = priceEl?.textContent?.trim() || `No Price Found ${i}`;
              const link = linkEl?.href || '';
              const price = 0; // Don't even try to parse price
              const itemId = ''; // Don't even try to extract item ID

              console.log(`📦 RAW EXTRACT ${i + 1}: "${title}" - ${priceText}`);

              products.push({
                title: title,
                price: price,
                priceText: priceText,
                link: link,
                itemId: itemId,
                source: 'universal-handler-zero-filtering',
                index: i,
                timestamp: Date.now()
              });
            }

            // ✅ NO EMERGENCY EXTRACTION - Just process what we found

            console.log(`✅ Universal handler: Extracted ${products.length} products`);
            console.log('🔍 Universal handler: Sample products:', products.slice(0, 3));
            console.log('🔍 Universal handler: Sending response with products array');

            sendResponse({
              success: true,
              products: products,
              handler: 'universal-ebay-handler',
              action: message.action,
              mode: 'universal-scraping',
              totalFound: products.length,
              url: window.location.href,
              timestamp: Date.now()
            });
          } catch (error) {
            console.error('❌ Universal handler scraping failed:', error);
            sendResponse({
              success: false,
              error: 'Universal scraping failed: ' + error.message,
              handler: 'universal-ebay-handler'
            });
          }
        })();

        return true; // Keep message channel open
      }

      // ✅ FIXED: Always handle startScraping in universal handler
      // The search handler delegation was causing the scraping to fail
      console.log('🔄 Universal handler: All messages handled by universal handler for reliability');
      


      // Handle other basic messages
      console.log('❓ Universal handler - unhandled message:', message.action);
      sendResponse({
        success: false,
        error: 'Message not handled by universal handler',
        action: message.action,
        handler: 'universal-ebay-handler',
        suggestion: 'This message might need the search page handler'
      });
      
    } catch (error) {
      console.error('❌ Universal handler error:', error);
      sendResponse({
        success: false,
        error: 'Universal handler crashed: ' + error.message,
        action: message.action
      });
    }
    
    return true;
  }
  
  /**
   * Check if current page is a search page
   */
  function isSearchPage() {
    const url = window.location.href;
    return url.includes('/sch/i.html') && url.includes('_nkw=');
  }
  
  /**
   * Check if message is search-specific
   */
  function isSearchSpecificMessage(action) {
    const searchActions = [
      'startScraping',
      'testSelectors', 
      'minimalTest',
      'scanCurrentPage'
    ];
    return searchActions.includes(action);
  }
  
  // ✅ CRITICAL: Register message listener immediately
  chrome.runtime.onMessage.addListener(handleMessage);
  
  // Send ready notification to service worker
  function notifyServiceWorker() {
    chrome.runtime.sendMessage({
      action: 'contentScriptReady',
      handler: 'universal-ebay-handler',
      url: window.location.href,
      timestamp: Date.now(),
      isSearchPage: isSearchPage()
    }).catch(error => {
      console.log('📡 Service worker not ready yet for universal handler');
    });
  }
  
  // Notify service worker when ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', notifyServiceWorker);
  } else {
    notifyServiceWorker();
  }
  
  // Global status for debugging
  window.universalEbayHandler = {
    isReady: () => true,
    getUrl: () => window.location.href,
    isSearchPage: isSearchPage
  };
  
  console.log('✅ Universal eBay Handler setup complete');
  
})();
